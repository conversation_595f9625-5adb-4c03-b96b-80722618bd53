// 性能监控和分析工具

// 内存使用监控
export const getMemoryUsage = (): {
  used: number;
  total: number;
  percentage: number;
} => {
  const memory = (performance as any).memory;
  
  if (!memory) {
    return { used: 0, total: 0, percentage: 0 };
  }
  
  return {
    used: memory.usedJSHeapSize,
    total: memory.totalJSHeapSize,
    percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
  };
};

// 渲染性能监控
export const measureRenderTime = <T extends any[]>(
  fn: (...args: T) => void,
  name: string = 'render'
) => {
  return (...args: T) => {
    const startTime = performance.now();
    const result = fn(...args);
    const endTime = performance.now();
    
    console.log(`${name} took ${(endTime - startTime).toFixed(2)}ms`);
    
    return result;
  };
};

// 防抖函数性能优化版本
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate: boolean = false
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  let lastCallTime = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    const callNow = immediate && !timeout;
    
    if (timeout) {
      clearTimeout(timeout);
    }
    
    timeout = setTimeout(() => {
      timeout = null;
      if (!immediate) {
        func(...args);
      }
    }, wait);
    
    if (callNow) {
      func(...args);
    }
    
    lastCallTime = now;
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => {
        inThrottle = false;
      }, limit);
    }
  };
};

// 长任务检测
export const detectLongTasks = (threshold: number = 50): void => {
  if ('PerformanceObserver' in window) {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > threshold) {
          console.warn(`Long task detected: ${entry.duration.toFixed(2)}ms`, entry);
        }
      });
    });
    
    observer.observe({ entryTypes: ['longtask'] });
  }
};

// FPS监控
export class FPSMonitor {
  private frameCount = 0;
  private lastTime = performance.now();
  private fps = 0;
  private isRunning = false;
  private animationId: number | null = null;

  start(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.frameCount = 0;
    this.lastTime = performance.now();
    this.measure();
  }

  stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  getFPS(): number {
    return this.fps;
  }

  private measure = (): void => {
    if (!this.isRunning) return;
    
    this.frameCount++;
    const currentTime = performance.now();
    
    if (currentTime >= this.lastTime + 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
      this.frameCount = 0;
      this.lastTime = currentTime;
    }
    
    this.animationId = requestAnimationFrame(this.measure);
  };
}

// 组件渲染次数监控
export const createRenderCounter = (componentName: string) => {
  let renderCount = 0;
  
  return {
    increment: () => {
      renderCount++;
      console.log(`${componentName} rendered ${renderCount} times`);
    },
    getCount: () => renderCount,
    reset: () => {
      renderCount = 0;
    },
  };
};

// 内存泄漏检测
export const detectMemoryLeaks = (
  testFunction: () => void,
  iterations: number = 10,
  threshold: number = 5 * 1024 * 1024 // 5MB
): Promise<boolean> => {
  return new Promise((resolve) => {
    const initialMemory = getMemoryUsage().used;
    
    const runTest = (iteration: number) => {
      if (iteration >= iterations) {
        // 强制垃圾回收（如果可用）
        if ((global as any).gc) {
          (global as any).gc();
        }
        
        setTimeout(() => {
          const finalMemory = getMemoryUsage().used;
          const memoryIncrease = finalMemory - initialMemory;
          
          console.log(`Memory increase after ${iterations} iterations: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
          
          resolve(memoryIncrease > threshold);
        }, 100);
        
        return;
      }
      
      testFunction();
      
      // 使用setTimeout来避免阻塞主线程
      setTimeout(() => runTest(iteration + 1), 10);
    };
    
    runTest(0);
  });
};

// 性能报告生成器
export const generatePerformanceReport = (): {
  memory: ReturnType<typeof getMemoryUsage>;
  timing: PerformanceTiming;
  navigation: PerformanceNavigation;
} => {
  return {
    memory: getMemoryUsage(),
    timing: performance.timing,
    navigation: performance.navigation,
  };
};

// React DevTools Profiler 辅助函数
export const logProfilerData = (
  id: string,
  phase: 'mount' | 'update',
  actualDuration: number,
  baseDuration: number,
  startTime: number,
  commitTime: number
): void => {
  console.log(`Profiler [${id}]:`, {
    phase,
    actualDuration: `${actualDuration.toFixed(2)}ms`,
    baseDuration: `${baseDuration.toFixed(2)}ms`,
    startTime: `${startTime.toFixed(2)}ms`,
    commitTime: `${commitTime.toFixed(2)}ms`,
  });
};
