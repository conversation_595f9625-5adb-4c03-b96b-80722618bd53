import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Input, Button, AutoComplete } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import HomePageMap from '../components/Map/HomePageMap';
import StatisticsPanel from '../components/HomePage/StatisticsPanel';
import LegendPanel from '../components/HomePage/LegendPanel';
import QuickActionsPanel from '../components/HomePage/QuickActionsPanel';
import PerformanceProfiler from '../components/Performance/PerformanceProfiler';

const HomePage: React.FC = memo(() => {
  const [searchValue, setSearchValue] = useState('');
  const [searchOptions, setSearchOptions] = useState<{ value: string }[]>([]);
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // 添加缺失的loading状态
  const [mapData, setMapData] = useState({
    totalSpecies: 1247,
    totalRecordings: 8936,
    activeHotspots: 342,
    onlineUsers: 156
  });

  // 检测移动设备 - 添加防抖优化
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const checkMobile = () => {
      // 防抖处理，避免频繁触发
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsMobile(window.innerWidth < 768);
      }, 100);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timeoutId); // 清理定时器
    };
  }, []);

  // 预定义搜索建议数据，避免每次重新创建
  const mockSuggestionsData = useMemo(() => [
    { value: '座头鲸 (Megaptera novaeangliae)' },
    { value: '蓝鲸 (Balaenoptera musculus)' },
    { value: '虎鲸 (Orcinus orca)' },
    { value: '太平洋' },
    { value: '大西洋' },
  ], []);

  // 处理搜索输入变化 - 优化性能
  const handleSearchChange = useCallback((value: string) => {
    setSearchValue(value);

    if (value) {
      // 使用预定义的数据进行过滤
      const filteredSuggestions = mockSuggestionsData.filter(item =>
        item.value.toLowerCase().includes(value.toLowerCase())
      );

      setSearchOptions(filteredSuggestions);
    } else {
      setSearchOptions([]);
    }
  }, [mockSuggestionsData]);

  // 处理搜索 - 添加取消机制防止内存泄漏
  const handleSearch = useCallback(async (value: string) => {
    if (!value.trim()) return;

    setIsLoading(true);

    // 创建AbortController来支持取消操作
    const abortController = new AbortController();

    try {
      // 模拟搜索API调用
      await new Promise((resolve, reject) => {
        const timeoutId = setTimeout(resolve, 1000);

        // 监听取消信号
        abortController.signal.addEventListener('abort', () => {
          clearTimeout(timeoutId);
          reject(new Error('搜索已取消'));
        });
      });

      console.log('搜索:', value);
    } catch (error) {
      if (error instanceof Error && error.message !== '搜索已取消') {
        console.error('搜索失败:', error);
      }
    } finally {
      setIsLoading(false);
    }

    // 返回清理函数
    return () => {
      abortController.abort();
    };
  }, []);

  // 处理地图数据加载 - 添加类型定义
  const handleDataLoad = useCallback((data: Partial<typeof mapData>) => {
    setMapData(prev => ({ ...prev, ...data }));
  }, []);

  return (
    <PerformanceProfiler id="HomePage">
      <div style={{
        height: 'calc(100vh - 80px)', // 减去header高度
        display: 'flex',
        flexDirection: 'column',
        background: '#f8f9fa',
        position: 'relative'
      }}>

      {/* 主要内容区域 - 地图占满屏幕 */}
      <div style={{
        flex: 1,
        display: 'flex',
        position: 'relative',
        overflow: 'hidden'
      }}>
        {/* 地图区域 - 占据大部分空间 */}
        <div style={{
          flex: 1,
          position: 'relative',
          background: 'white'
        }}>
          <HomePageMap
            height="100%"
            onDataLoad={handleDataLoad}
          />
        </div>

        {/* 右侧边栏 - 使用优化的组件 */}
        {!isMobile && (
          <div style={{
            width: '320px',
            background: 'white',
            borderLeft: '1px solid #e5e7eb',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}>
            {/* 统计数据面板 */}
            <StatisticsPanel mapData={mapData} />

            {/* 图例面板 */}
            <LegendPanel />

            {/* 快捷操作面板 */}
            <QuickActionsPanel />
          </div>
        )}
      </div>
    </div>
    </PerformanceProfiler>
  );
});

// 设置displayName以便调试
HomePage.displayName = 'HomePage';

export default HomePage;
