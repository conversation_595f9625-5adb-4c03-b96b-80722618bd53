# 地图服务配置指南

## 概述

本项目已将地图底图服务更换为符合中华人民共和国测绘法和地图管理条例要求的地图服务，确保台湾地区在地图上正确显示为中国领土的一部分。

## 支持的地图服务

### 1. 天地图（推荐）
- **服务商**: 国家地理信息公共服务平台
- **官方网站**: https://www.tianditu.gov.cn/
- **特点**: 
  - 官方地图服务，完全符合中国地图审查要求
  - 提供影像和矢量两种底图类型
  - 需要申请API密钥
- **类型**:
  - `TIANDITU_SATELLITE`: 天地图影像地图
  - `TIANDITU_VECTOR`: 天地图矢量地图

### 2. 高德地图
- **服务商**: 高德软件有限公司
- **官方网站**: https://www.amap.com/
- **特点**: 
  - 符合中国地图审查要求
  - 商业地图服务
  - 基础瓦片服务无需API密钥
- **类型**: `AMAP`

### 3. 百度地图
- **服务商**: 百度在线网络技术（北京）有限公司
- **官方网站**: https://map.baidu.com/
- **特点**: 
  - 符合中国地图审查要求
  - 商业地图服务
  - 基础瓦片服务无需API密钥
- **类型**: `BAIDU`

## 配置方法

### 1. 环境变量配置

在 `frontend/.env` 文件中添加以下配置：

```env
# 天地图API密钥（推荐使用）
VITE_TIANDITU_KEY=your-tianditu-api-key

# 地图默认配置
VITE_MAP_DEFAULT_CENTER_LAT=35.0
VITE_MAP_DEFAULT_CENTER_LNG=104.0
VITE_MAP_DEFAULT_ZOOM=4
```

### 2. 申请天地图API密钥

1. 访问 [天地图开发者平台](https://console.tianditu.gov.cn/)
2. 注册开发者账号
3. 创建应用并获取API密钥
4. 将密钥配置到环境变量中

### 3. 代码中使用

```typescript
import { MapServiceType } from '../config/mapConfig';

// 在组件中使用
<BaseMap
  center={[35.0, 104.0]}
  zoom={4}
  mapService={MapServiceType.TIANDITU_SATELLITE}
/>
```

## 地图服务切换

项目提供了 `MapServiceSelector` 组件，允许用户在运行时切换不同的地图服务：

```typescript
import MapServiceSelector from './MapServiceSelector';

<MapServiceSelector
  value={currentMapService}
  onChange={setMapService}
  size="small"
/>
```

## 合规性说明

### 符合的法规要求

1. **中华人民共和国测绘法**
2. **地图管理条例**
3. **互联网地图服务专业标准**

### 地图显示要求

- ✅ 台湾地区显示为中国领土的一部分
- ✅ 南海诸岛完整显示
- ✅ 国界线符合中国官方标准
- ✅ 地名标注使用中文

### 避免的问题

- ❌ 使用可能存在政治敏感性的海外地图服务
- ❌ 台湾地区显示为独立区域
- ❌ 缺失南海九段线
- ❌ 使用不符合中国标准的地图投影

## 技术实现

### 文件结构

```
frontend/src/
├── config/
│   └── mapConfig.ts          # 地图服务配置
├── components/Map/
│   ├── BaseMap.tsx           # 基础地图组件
│   ├── MapServiceSelector.tsx # 地图服务选择器
│   └── InteractiveMap.tsx    # 交互式地图组件
└── MAP_SERVICE_GUIDE.md      # 本文档
```

### 配置文件说明

`mapConfig.ts` 文件包含了所有支持的地图服务配置，包括：
- 瓦片服务URL
- 服务商信息
- 缩放级别限制
- API密钥要求

## 故障排除

### 常见问题

1. **地图无法加载**
   - 检查网络连接
   - 确认API密钥是否正确配置
   - 查看浏览器控制台错误信息

2. **天地图显示空白**
   - 确认已申请并配置天地图API密钥
   - 检查密钥是否有效且未过期
   - 确认域名已在天地图控制台中配置

3. **地图显示不完整**
   - 检查缩放级别是否在支持范围内
   - 确认地图容器尺寸设置正确

### 联系支持

如遇到技术问题，请：
1. 查看浏览器开发者工具的控制台错误
2. 检查网络请求是否成功
3. 确认配置文件是否正确

## 更新日志

- **2024-01-XX**: 初始版本，支持天地图、高德地图、百度地图
- **2024-01-XX**: 添加地图服务选择器组件
- **2024-01-XX**: 完善配置文档和使用指南
